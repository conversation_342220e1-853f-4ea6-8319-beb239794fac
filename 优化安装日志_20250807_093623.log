2025-08-07 09:36:23,380 - INFO - 当前 Pillow 版本: 10.4.0
2025-08-07 09:36:23,380 - INFO - ⚠️ 当前使用标准 Pillow，可以升级到 SIMD 版本
2025-08-07 09:36:27,366 - INFO - 开始安装 Pillow-SIMD...
2025-08-07 09:36:27,366 - INFO - 步骤 1/3: 卸载现有 Pillow...
2025-08-07 09:36:28,861 - INFO - ✅ 已卸载现有 Pillow
2025-08-07 09:36:28,862 - INFO - 步骤 2/3: 安装 Pillow-SIMD...
2025-08-07 09:36:35,160 - WARNING - ⚠️ Pillow-SIMD 安装失败，尝试备用方案...
2025-08-07 09:36:35,161 - INFO - 错误信息: WARNING: Ignoring invalid distribution ~ywebview (C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages)
  error: subprocess-exited-with-error
  
  Building wheel for pillow-simd (pyproject.toml) did not run successfully.
  exit code: 1
  
  [246 lines of output]
  C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\dist.py:759: SetuptoolsDeprecationWarning: License classifiers are deprecated.
  !!
  
          ********************************************************************************
          Please consider removing the following classifiers in favor of a SPDX license expression:
  
          License :: OSI Approved :: Historical Permission Notice and Disclaimer (HPND)
  
          See https://packaging.python.org/en/latest/guides/writing-pyproject-toml/#license for details.
          ********************************************************************************
  
  !!
    self._finalize_license_expression()
  running bdist_wheel
  running build
  running build_py
  creating build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BdfFontFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BlpImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BmpImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BufrStubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ContainerIO.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\CurImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\DcxImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\DdsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\EpsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ExifTags.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\features.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FitsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FitsStubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FliImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FontFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FpxImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FtexImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GbrImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GdImageFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GifImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GimpGradientFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GimpPaletteFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GribStubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\Hdf5StubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\IcnsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\IcoImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\Image.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageChops.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageCms.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageColor.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageDraw.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageDraw2.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageEnhance.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageFilter.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageFont.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageGrab.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageMath.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageMode.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageMorph.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageOps.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImagePalette.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImagePath.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageQt.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageSequence.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageShow.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageStat.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageTk.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageTransform.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageWin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImtImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\IptcImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\Jpeg2KImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\JpegImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\JpegPresets.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\McIdasImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MicImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MpegImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MpoImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MspImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PaletteFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PalmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PcdImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PcfFontFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PcxImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PdfImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PdfParser.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PixarImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PngImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PpmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PsdImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PSDraw.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PyAccess.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\QoiImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\SgiImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\SpiderImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\SunImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TarIO.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TgaImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TiffImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TiffTags.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\WalImageFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\WebPImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\WmfImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\XbmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\XpmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\XVThumbImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_binary.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_deprecate.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_tkinter_finder.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_util.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_version.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\__init__.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\__main__.py -> build\lib.win-amd64-cpython-313\PIL
  running egg_info
  writing src\Pillow_SIMD.egg-info\PKG-INFO
  writing dependency_links to src\Pillow_SIMD.egg-info\dependency_links.txt
  writing requirements to src\Pillow_SIMD.egg-info\requires.txt
  writing top-level names to src\Pillow_SIMD.egg-info\top_level.txt
  reading manifest file 'src\Pillow_SIMD.egg-info\SOURCES.txt'
  reading manifest template 'MANIFEST.in'
  warning: no files found matching '*.c'
  warning: no files found matching '*.h'
  warning: no files found matching '*.sh'
  warning: no files found matching '*.txt'
  warning: no previously-included files found matching '.appveyor.yml'
  warning: no previously-included files found matching '.clang-format'
  warning: no previously-included files found matching '.coveragerc'
  warning: no previously-included files found matching '.editorconfig'
  warning: no previously-included files found matching '.readthedocs.yml'
  warning: no previously-included files found matching 'codecov.yml'
  warning: no previously-included files found matching 'renovate.json'
  warning: no previously-included files matching '.DS_Store' found anywhere in distribution
  warning: no previously-included files matching '.git*' found anywhere in distribution
  warning: no previously-included files matching '*.pyc' found anywhere in distribution
  warning: no previously-included files matching '*.so' found anywhere in distribution
  no previously-included directories found matching '.ci'
  adding license file 'LICENSE'
  writing manifest file 'src\Pillow_SIMD.egg-info\SOURCES.txt'
  running build_ext
  
  
  The headers or library files could not be found for zlib,
  a required dependency when compiling Pillow from source.
  
  Please see the install instructions at:
     https://pillow.readthedocs.io/en/latest/installation.html
  
  Traceback (most recent call last):
    File "<string>", line 994, in <module>
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\__init__.py", line 115, in setup
      return distutils.core.setup(**attrs)
             ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\core.py", line 186, in setup
      return run_commands(dist)
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\core.py", line 202, in run_commands
      dist.run_commands()
      ~~~~~~~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1002, in run_commands
      self.run_command(cmd)
      ~~~~~~~~~~~~~~~~^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\dist.py", line 1102, in run_command
      super().run_command(command)
      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
      cmd_obj.run()
      ~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\command\bdist_wheel.py", line 370, in run
      self.run_command("build")
      ~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\cmd.py", line 357, in run_command
      self.distribution.run_command(command)
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\dist.py", line 1102, in run_command
      super().run_command(command)
      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
      cmd_obj.run()
      ~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\command\build.py", line 135, in run
      self.run_command(cmd_name)
      ~~~~~~~~~~~~~~~~^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\cmd.py", line 357, in run_command
      self.distribution.run_command(command)
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\dist.py", line 1102, in run_command
      super().run_command(command)
      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
      cmd_obj.run()
      ~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\command\build_ext.py", line 96, in run
      _build_ext.run(self)
      ~~~~~~~~~~~~~~^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\_distutils\command\build_ext.py", line 368, in run
      self.build_extensions()
      ~~~~~~~~~~~~~~~~~~~~~^^
    File "<string>", line 810, in build_extensions
  RequiredDependencyException: zlib
  
  During handling of the above exception, another exception occurred:
  
  Traceback (most recent call last):
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py", line 389, in <module>
      main()
      ~~~~^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py", line 373, in main
      json_out["return_val"] = hook(**hook_input["kwargs"])
                               ~~~~^^^^^^^^^^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py", line 280, in build_wheel
      return _build_backend().build_wheel(
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
          wheel_directory, config_settings, metadata_directory
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      )
      ^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\build_meta.py", line 435, in build_wheel
      return _build(['bdist_wheel', '--dist-info-dir', str(metadata_directory)])
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\build_meta.py", line 423, in _build
      return self._build_with_temp_dir(
             ~~~~~~~~~~~~~~~~~~~~~~~~~^
          cmd,
          ^^^^
      ...<3 lines>...
          self._arbitrary_args(config_settings),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      )
      ^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\build_meta.py", line 404, in _build_with_temp_dir
      self.run_setup()
      ~~~~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\build_meta.py", line 512, in run_setup
      super().run_setup(setup_script=setup_script)
      ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-so7l_40q\overlay\Lib\site-packages\setuptools\build_meta.py", line 317, in run_setup
      exec(code, locals())
      ~~~~^^^^^^^^^^^^^^^^
    File "<string>", line 1011, in <module>
  RequiredDependencyException:
  
  The headers or library files could not be found for zlib,
  a required dependency when compiling Pillow from source.
  
  Please see the install instructions at:
     https://pillow.readthedocs.io/en/latest/installation.html
  
  
  <string>:46: RuntimeWarning: Pillow 9.5.0.post2 does not support Python 3.13 and does not provide prebuilt Windows binaries. We do not recommend building from source on Windows.
  [end of output]
  
  note: This error originates from a subprocess, and is likely not a problem with pip.
  ERROR: Failed building wheel for pillow-simd

[notice] A new release of pip is available: 25.1.1 -> 25.2
[notice] To update, run: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install --upgrade pip
ERROR: Failed to build installable wheels for some pyproject.toml based projects (pillow-simd)

2025-08-07 09:36:35,181 - INFO - 尝试从预编译包安装...
2025-08-07 09:36:41,345 - ERROR - ❌ 安装过程出错: Command '['C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python313\\python.exe', '-m', 'pip', 'install', '--upgrade', '--force-reinstall', 'pillow-simd']' returned non-zero exit status 1.
2025-08-07 09:36:41,346 - ERROR - 错误输出: WARNING: Ignoring invalid distribution ~ywebview (C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages)
  error: subprocess-exited-with-error
  
  Building wheel for pillow-simd (pyproject.toml) did not run successfully.
  exit code: 1
  
  [246 lines of output]
  C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\dist.py:759: SetuptoolsDeprecationWarning: License classifiers are deprecated.
  !!
  
          ********************************************************************************
          Please consider removing the following classifiers in favor of a SPDX license expression:
  
          License :: OSI Approved :: Historical Permission Notice and Disclaimer (HPND)
  
          See https://packaging.python.org/en/latest/guides/writing-pyproject-toml/#license for details.
          ********************************************************************************
  
  !!
    self._finalize_license_expression()
  running bdist_wheel
  running build
  running build_py
  creating build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BdfFontFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BlpImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BmpImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\BufrStubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ContainerIO.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\CurImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\DcxImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\DdsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\EpsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ExifTags.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\features.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FitsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FitsStubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FliImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FontFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FpxImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\FtexImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GbrImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GdImageFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GifImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GimpGradientFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GimpPaletteFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\GribStubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\Hdf5StubImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\IcnsImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\IcoImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\Image.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageChops.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageCms.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageColor.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageDraw.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageDraw2.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageEnhance.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageFilter.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageFont.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageGrab.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageMath.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageMode.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageMorph.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageOps.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImagePalette.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImagePath.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageQt.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageSequence.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageShow.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageStat.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageTk.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageTransform.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImageWin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\ImtImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\IptcImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\Jpeg2KImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\JpegImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\JpegPresets.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\McIdasImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MicImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MpegImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MpoImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\MspImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PaletteFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PalmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PcdImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PcfFontFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PcxImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PdfImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PdfParser.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PixarImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PngImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PpmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PsdImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PSDraw.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\PyAccess.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\QoiImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\SgiImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\SpiderImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\SunImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TarIO.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TgaImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TiffImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\TiffTags.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\WalImageFile.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\WebPImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\WmfImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\XbmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\XpmImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\XVThumbImagePlugin.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_binary.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_deprecate.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_tkinter_finder.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_util.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\_version.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\__init__.py -> build\lib.win-amd64-cpython-313\PIL
  copying src\PIL\__main__.py -> build\lib.win-amd64-cpython-313\PIL
  running egg_info
  writing src\Pillow_SIMD.egg-info\PKG-INFO
  writing dependency_links to src\Pillow_SIMD.egg-info\dependency_links.txt
  writing requirements to src\Pillow_SIMD.egg-info\requires.txt
  writing top-level names to src\Pillow_SIMD.egg-info\top_level.txt
  reading manifest file 'src\Pillow_SIMD.egg-info\SOURCES.txt'
  reading manifest template 'MANIFEST.in'
  warning: no files found matching '*.c'
  warning: no files found matching '*.h'
  warning: no files found matching '*.sh'
  warning: no files found matching '*.txt'
  warning: no previously-included files found matching '.appveyor.yml'
  warning: no previously-included files found matching '.clang-format'
  warning: no previously-included files found matching '.coveragerc'
  warning: no previously-included files found matching '.editorconfig'
  warning: no previously-included files found matching '.readthedocs.yml'
  warning: no previously-included files found matching 'codecov.yml'
  warning: no previously-included files found matching 'renovate.json'
  warning: no previously-included files matching '.DS_Store' found anywhere in distribution
  warning: no previously-included files matching '.git*' found anywhere in distribution
  warning: no previously-included files matching '*.pyc' found anywhere in distribution
  warning: no previously-included files matching '*.so' found anywhere in distribution
  no previously-included directories found matching '.ci'
  adding license file 'LICENSE'
  writing manifest file 'src\Pillow_SIMD.egg-info\SOURCES.txt'
  running build_ext
  
  
  The headers or library files could not be found for zlib,
  a required dependency when compiling Pillow from source.
  
  Please see the install instructions at:
     https://pillow.readthedocs.io/en/latest/installation.html
  
  Traceback (most recent call last):
    File "<string>", line 994, in <module>
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\__init__.py", line 115, in setup
      return distutils.core.setup(**attrs)
             ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\core.py", line 186, in setup
      return run_commands(dist)
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\core.py", line 202, in run_commands
      dist.run_commands()
      ~~~~~~~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1002, in run_commands
      self.run_command(cmd)
      ~~~~~~~~~~~~~~~~^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\dist.py", line 1102, in run_command
      super().run_command(command)
      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
      cmd_obj.run()
      ~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\command\bdist_wheel.py", line 370, in run
      self.run_command("build")
      ~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\cmd.py", line 357, in run_command
      self.distribution.run_command(command)
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\dist.py", line 1102, in run_command
      super().run_command(command)
      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
      cmd_obj.run()
      ~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\command\build.py", line 135, in run
      self.run_command(cmd_name)
      ~~~~~~~~~~~~~~~~^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\cmd.py", line 357, in run_command
      self.distribution.run_command(command)
      ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\dist.py", line 1102, in run_command
      super().run_command(command)
      ~~~~~~~~~~~~~~~~~~~^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\dist.py", line 1021, in run_command
      cmd_obj.run()
      ~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\command\build_ext.py", line 96, in run
      _build_ext.run(self)
      ~~~~~~~~~~~~~~^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\_distutils\command\build_ext.py", line 368, in run
      self.build_extensions()
      ~~~~~~~~~~~~~~~~~~~~~^^
    File "<string>", line 810, in build_extensions
  RequiredDependencyException: zlib
  
  During handling of the above exception, another exception occurred:
  
  Traceback (most recent call last):
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py", line 389, in <module>
      main()
      ~~~~^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py", line 373, in main
      json_out["return_val"] = hook(**hook_input["kwargs"])
                               ~~~~^^^^^^^^^^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\pip\_vendor\pyproject_hooks\_in_process\_in_process.py", line 280, in build_wheel
      return _build_backend().build_wheel(
             ~~~~~~~~~~~~~~~~~~~~~~~~~~~~^
          wheel_directory, config_settings, metadata_directory
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      )
      ^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\build_meta.py", line 435, in build_wheel
      return _build(['bdist_wheel', '--dist-info-dir', str(metadata_directory)])
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\build_meta.py", line 423, in _build
      return self._build_with_temp_dir(
             ~~~~~~~~~~~~~~~~~~~~~~~~~^
          cmd,
          ^^^^
      ...<3 lines>...
          self._arbitrary_args(config_settings),
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
      )
      ^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\build_meta.py", line 404, in _build_with_temp_dir
      self.run_setup()
      ~~~~~~~~~~~~~~^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\build_meta.py", line 512, in run_setup
      super().run_setup(setup_script=setup_script)
      ~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^
    File "C:\Users\<USER>\AppData\Local\Temp\pip-build-env-qsmnva1a\overlay\Lib\site-packages\setuptools\build_meta.py", line 317, in run_setup
      exec(code, locals())
      ~~~~^^^^^^^^^^^^^^^^
    File "<string>", line 1011, in <module>
  RequiredDependencyException:
  
  The headers or library files could not be found for zlib,
  a required dependency when compiling Pillow from source.
  
  Please see the install instructions at:
     https://pillow.readthedocs.io/en/latest/installation.html
  
  
  <string>:46: RuntimeWarning: Pillow 9.5.0.post2 does not support Python 3.13 and does not provide prebuilt Windows binaries. We do not recommend building from source on Windows.
  [end of output]
  
  note: This error originates from a subprocess, and is likely not a problem with pip.
  ERROR: Failed building wheel for pillow-simd

[notice] A new release of pip is available: 25.1.1 -> 25.2
[notice] To update, run: C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe -m pip install --upgrade pip
ERROR: Failed to build installable wheels for some pyproject.toml based projects (pillow-simd)

